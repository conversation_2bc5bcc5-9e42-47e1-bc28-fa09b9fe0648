<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */
$routes->get('/', 'Home::index');


$routes->group("dashboard",["namespace"=>"App\Controllers"],function($routes){
    $routes->GET('/', 'DashboardController::index');
});

// Sales Routes
$routes->group('sales', ['namespace' => 'App\Controllers'], function ($routes) {
    $routes->get('/', 'SalesController::dashboard');
    $routes->get('dashboard', 'SalesController::dashboard');
    $routes->get('create', 'SalesController::create');
    $routes->get('list', 'SalesController::list');
    $routes->get('export', 'SalesController::export');
    $routes->get('domestic', 'SalesController::domestic');
    $routes->get('settings', 'SalesController::settings');
});

// Test route for top navigation
$routes->get('test/navigation', function() {
    return view('sales/test_navigation');
});

// Test route for combined navigation
$routes->get('test/combined-navigation', function() {
    return view('sales/combined_navigation_test');
});

// Reports Routes
$routes->group('reports', ['namespace' => 'App\Controllers'], function ($routes) {
    $routes->get('sales', 'ReportsController::sales');
    $routes->get('performance', 'ReportsController::performance');
    $routes->get('analytics', 'ReportsController::analytics');
});

// Admin Routes
$routes->group('admin', ['namespace' => 'App\Controllers\Admin'], function ($routes) {
    $routes->get('/', 'DashboardController::index');
    $routes->get('dashboard', 'DashboardController::index');
});