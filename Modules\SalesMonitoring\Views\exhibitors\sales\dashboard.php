<?= $this->extend('Modules\SalesMonitoring\Views\layouts\sales_layout') ?>

<?= $this->section('title') ?>Sales Dashboard - Trade Expo<?= $this->endSection() ?>

<?= $this->section('page_title') ?>Sales Dashboard<?= $this->endSection() ?>
<?= $this->section('page_subtitle') ?>Manila Fame 2025 Performance Overview<?= $this->endSection() ?>

<?= $this->section('page_actions') ?>
<button type="button" class="btn btn-primary" onclick="openSalesDrawer()" data-aos="fade-left">
    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="12" y1="5" x2="12" y2="19"/><line x1="5" y1="12" x2="19" y2="12"/></svg>
    Add Sales
</button>
<button type="button" class="btn btn-outline-primary" data-aos="fade-left" data-aos-delay="100">
    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="9" cy="7" r="4"/><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/><path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/></svg>
    Inquiries
</button>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<!-- Sales Statistics Cards -->
<div class="row row-deck row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="100">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Export Sales</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-primary text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="9"/><path d="M12 3c7.2 0 9 1.8 9 9s-1.8 9 -9 9s-9 -1.8 -9 -9s1.8 -9 9 -9z"/><path d="M8 12h8"/><path d="M12 8v8"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-primary">$52,300</div>
                <div class="d-flex mb-2">
                    <div>Booked Sales</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            12%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-primary" style="width: 75%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="200">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Export Negotiations</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-warning text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M8 9h8"/><path d="M8 13h6"/><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-warning">$7,890</div>
                <div class="d-flex mb-2">
                    <div>Under Negotiation</div>
                    <div class="ms-auto">
                        <span class="text-yellow d-inline-flex align-items-center lh-1">
                            5%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="5" y1="12" x2="19" y2="12"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-warning" style="width: 45%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="300">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Domestic Sales</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-success text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="5 12 3 12 12 3 21 12 19 12"/><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7"/><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-success">₱24,300</div>
                <div class="d-flex mb-2">
                    <div>Local Market</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            8%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-success" style="width: 60%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="400">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Retail Sales</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-info text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="6" cy="19" r="2"/><circle cx="17" cy="19" r="2"/><path d="M17 17h-11v-14h-2"/><path d="M6 5l14 1l-1 7h-13"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-info">₱18,500</div>
                <div class="d-flex mb-2">
                    <div>Direct Sales</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            15%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-info" style="width: 80%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Additional Metrics -->
<div class="row row-deck row-cards mb-4">
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="500">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Buyers Met</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-purple text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="9" cy="7" r="4"/><path d="M3 21v-2a4 4 0 0 1 4 -4h4a4 4 0 0 1 4 4v2"/><path d="M16 3.13a4 4 0 0 1 0 7.75"/><path d="M21 21v-2a4 4 0 0 0 -3 -3.85"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-purple">52</div>
                <div class="d-flex mb-2">
                    <div>Total Contacts</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            +8
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-purple" style="width: 85%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="600">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Active Inquiries</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-orange text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M8 9h8"/><path d="M8 13h6"/><path d="M18 4a3 3 0 0 1 3 3v8a3 3 0 0 1 -3 3h-5l-5 3v-3h-2a3 3 0 0 1 -3 -3v-8a3 3 0 0 1 3 -3h12z"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-orange">22</div>
                <div class="d-flex mb-2">
                    <div>Pending Follow-up</div>
                    <div class="ms-auto">
                        <span class="text-yellow d-inline-flex align-items-center lh-1">
                            3 new
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-orange" style="width: 65%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="700">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Conversion Rate</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-teal text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><line x1="4" y1="19" x2="20" y2="19"/><polyline points="4,15 8,9 12,11 16,6 20,10"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-teal">68%</div>
                <div class="d-flex mb-2">
                    <div>Inquiry to Sale</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            +5%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-teal" style="width: 68%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-sm-6 col-lg-3">
        <div class="card sales-card" data-aos="fade-up" data-aos-delay="800">
            <div class="card-body">
                <div class="d-flex align-items-center">
                    <div class="subheader">Total Revenue</div>
                    <div class="ms-auto">
                        <div class="metric-icon bg-dark text-white">
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="9"/><path d="M14.8 9a2 2 0 0 0 -1.8 -1h-2a2 2 0 0 0 0 4h2a2 2 0 0 1 0 4h-2a2 2 0 0 1 -1.8 -1"/><path d="M12 6v2m0 8v2"/></svg>
                        </div>
                    </div>
                </div>
                <div class="h1 mb-3 text-dark">$95,190</div>
                <div class="d-flex mb-2">
                    <div>Combined Sales</div>
                    <div class="ms-auto">
                        <span class="text-green d-inline-flex align-items-center lh-1">
                            +18%
                            <svg xmlns="http://www.w3.org/2000/svg" class="icon ms-1" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><polyline points="3,17 9,11 13,15 21,7"/><polyline points="14,7 21,7 21,14"/></svg>
                        </span>
                    </div>
                </div>
                <div class="progress progress-sm">
                    <div class="progress-bar bg-dark" style="width: 90%" role="progressbar"></div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Sales Table -->
<div class="card" data-aos="fade-up" data-aos-delay="900">
    <div class="card-header">
        <h3 class="card-title">Recent Sales</h3>
        <div class="card-actions">
            <div class="dropdown">
                <a href="#" class="btn-action dropdown-toggle" data-bs-toggle="dropdown">
                    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><circle cx="12" cy="12" r="1"/><circle cx="12" cy="19" r="1"/><circle cx="12" cy="5" r="1"/></svg>
                </a>
                <div class="dropdown-menu dropdown-menu-end">
                    <a href="#" class="dropdown-item">Export to Excel</a>
                    <a href="#" class="dropdown-item">Export to PDF</a>
                    <div class="dropdown-divider"></div>
                    <a href="#" class="dropdown-item">View All Sales</a>
                </div>
            </div>
        </div>
    </div>
    <div class="table-responsive">
        <table class="table table-vcenter table-sales card-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Client</th>
                    <th>Product</th>
                    <th>Type</th>
                    <th>Amount</th>
                    <th>Status</th>
                    <th class="w-1">Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td>2023-11-15</td>
                    <td>
                        <div class="d-flex py-1 align-items-center">
                            <span class="avatar me-2" style="background-image: url(https://ui-avatars.com/api/?name=Global+Imports&background=206bc4&color=fff)"></span>
                            <div class="flex-fill">
                                <div class="font-weight-medium">Global Imports Inc.</div>
                                <div class="text-muted">USA</div>
                            </div>
                        </div>
                    </td>
                    <td>Handwoven Textiles</td>
                    <td><span class="badge bg-blue">Export</span></td>
                    <td class="text-muted">$12,500</td>
                    <td><span class="badge status-booked">Booked</span></td>
                    <td>
                        <div class="btn-list flex-nowrap">
                            <a href="#" class="btn btn-sm btn-outline-primary">Edit</a>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle align-text-top" data-bs-toggle="dropdown">Actions</button>
                                <div class="dropdown-menu dropdown-menu-end">
                                    <a class="dropdown-item" href="#">View Details</a>
                                    <a class="dropdown-item" href="#">Duplicate</a>
                                    <a class="dropdown-item text-danger" href="#">Delete</a>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>2023-11-14</td>
                    <td>
                        <div class="d-flex py-1 align-items-center">
                            <span class="avatar me-2" style="background-image: url(https://ui-avatars.com/api/?name=Domestic+Distributors&background=f59f00&color=fff)"></span>
                            <div class="flex-fill">
                                <div class="font-weight-medium">Domestic Distributors LLC</div>
                                <div class="text-muted">Philippines</div>
                            </div>
                        </div>
                    </td>
                    <td>Furniture Set</td>
                    <td><span class="badge bg-green">Domestic</span></td>
                    <td class="text-muted">₱185,200</td>
                    <td><span class="badge status-negotiation">Negotiation</span></td>
                    <td>
                        <div class="btn-list flex-nowrap">
                            <a href="#" class="btn btn-sm btn-outline-primary">Edit</a>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle align-text-top" data-bs-toggle="dropdown">Actions</button>
                                <div class="dropdown-menu dropdown-menu-end">
                                    <a class="dropdown-item" href="#">View Details</a>
                                    <a class="dropdown-item" href="#">Duplicate</a>
                                    <a class="dropdown-item text-danger" href="#">Delete</a>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>2023-11-13</td>
                    <td>
                        <div class="d-flex py-1 align-items-center">
                            <span class="avatar me-2" style="background-image: url(https://ui-avatars.com/api/?name=Retail+Chain&background=2fb344&color=fff)"></span>
                            <div class="flex-fill">
                                <div class="font-weight-medium">Retail Chain Co.</div>
                                <div class="text-muted">Philippines</div>
                            </div>
                        </div>
                    </td>
                    <td>Decorative Items</td>
                    <td><span class="badge bg-info">Retail</span></td>
                    <td class="text-muted">₱78,450</td>
                    <td><span class="badge status-booked">Booked</span></td>
                    <td>
                        <div class="btn-list flex-nowrap">
                            <a href="#" class="btn btn-sm btn-outline-primary">Edit</a>
                            <div class="dropdown">
                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle align-text-top" data-bs-toggle="dropdown">Actions</button>
                                <div class="dropdown-menu dropdown-menu-end">
                                    <a class="dropdown-item" href="#">View Details</a>
                                    <a class="dropdown-item" href="#">Duplicate</a>
                                    <a class="dropdown-item text-danger" href="#">Delete</a>
                                </div>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<!-- Include Sales Drawer Component -->
<?= $this->include('Modules\SalesMonitoring\Views\components\sales_drawer') ?>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
// Additional dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // Animate counters
    animateCounters();

    // Auto-refresh data every 30 seconds
    setInterval(refreshDashboardData, 30000);
});

function animateCounters() {
    const counters = document.querySelectorAll('.h1');

    counters.forEach(counter => {
        const target = counter.innerText.replace(/[^0-9]/g, '');
        if (target && !isNaN(target)) {
            const increment = target / 100;
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }

                // Preserve currency symbols and formatting
                const originalText = counter.innerText;
                const prefix = originalText.replace(/[0-9,]/g, '').split(target)[0];
                const suffix = originalText.replace(/[0-9,]/g, '').split(target)[1] || '';

                counter.innerText = prefix + Math.floor(current).toLocaleString() + suffix;
            }, 20);
        }
    });
}

function refreshDashboardData() {
    // Simulate data refresh with subtle animation
    const cards = document.querySelectorAll('.sales-card');
    cards.forEach((card, index) => {
        setTimeout(() => {
            card.style.transform = 'scale(1.02)';
            setTimeout(() => {
                card.style.transform = 'scale(1)';
            }, 200);
        }, index * 100);
    });

    // You can add actual AJAX call here to refresh data
    console.log('Dashboard data refreshed');
}

// Export functionality
function exportToExcel() {
    showAlert('Exporting to Excel...', 'info');
    // Add actual export logic here
}

function exportToPDF() {
    showAlert('Exporting to PDF...', 'info');
    // Add actual export logic here
}

// Quick actions
function viewAllSales() {
    window.location.href = '<?= base_url('sales/all') ?>';
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Ctrl/Cmd + N to open new sale drawer
    if ((e.ctrlKey || e.metaKey) && e.key === 'n') {
        e.preventDefault();
        openSalesDrawer();
    }

    // Ctrl/Cmd + R to refresh dashboard
    if ((e.ctrlKey || e.metaKey) && e.key === 'r') {
        e.preventDefault();
        refreshDashboardData();
    }
});

// Add click handlers for table actions
document.addEventListener('click', function(e) {
    if (e.target.matches('.btn-outline-primary')) {
        const row = e.target.closest('tr');
        if (row) {
            // Get sale data from row and populate edit form
            editSale(row);
        }
    }
});

function editSale(row) {
    // Extract data from table row
    const cells = row.querySelectorAll('td');
    const saleData = {
        date: cells[0].textContent,
        client: cells[1].querySelector('.font-weight-medium').textContent,
        product: cells[2].textContent,
        type: cells[3].querySelector('.badge').textContent.toLowerCase(),
        amount: cells[4].textContent,
        status: cells[5].querySelector('.badge').textContent.toLowerCase()
    };

    // Open drawer with pre-filled data
    openSalesDrawer();

    // Populate form (you can enhance this based on your needs)
    setTimeout(() => {
        if (saleData.type) {
            selectSaleType(saleData.type);
        }

        document.getElementById('saleDate').value = new Date(saleData.date).toISOString().split('T')[0];
        document.getElementById('buyerName').value = saleData.client;

        // Parse and set amount
        const amount = saleData.amount.replace(/[^0-9.]/g, '');
        document.getElementById('saleCost').value = amount;

        showAlert('Editing existing sale', 'info');
    }, 500);
}
</script>
<?= $this->endSection() ?>
