<?php

namespace Modules\SalesMonitoring\Controllers;

use App\Controllers\BaseController;
use CodeIgniter\HTTP\ResponseInterface;

use CodeIgniter\I18n\Time;
use CodeIgniter\Exceptions\PageNotFoundException;

use Modules\SalesMonitoring\Models\ExhibitorEventParticipationModel;

class DashboardController extends BaseController
{

    protected ExhibitorEventParticipationModel $participationModel;

    public function __construct()
    {
        $this->participationModel = new ExhibitorEventParticipationModel;
    }

    public function index()
    {
        $ff_code = session()->get('user');

        $events = $this->participationModel->getEventsByExhId($ff_code);

        foreach ($events as &$event) {
            $startDate = new \DateTime($event->start_date);
            $endDate = (clone $startDate)->modify('+' . ($event->no_of_days - 1) . ' days');

            if ($startDate->format('M') === $endDate->format('M')) {
                // Same month
                $event->formatted_date = $startDate->format('M j') . '-' . $endDate->format('j Y');
            } else {
                // Different month
                $event->formatted_date = $startDate->format('M j') . ' - ' . $endDate->format('M j Y');
            }
        }

        return view('Modules\SalesMonitoring\Views\exhibitors\events',['events' => $events]);
        
    }
}
