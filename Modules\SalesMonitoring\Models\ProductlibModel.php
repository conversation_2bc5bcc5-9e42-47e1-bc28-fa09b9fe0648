<?php

namespace Modules\SalesMonitoring\Models;

use CodeIgniter\Model;
use Psr\Log\LoggerInterface;
use \CodeIgniter\Database\Exceptions\DatabaseException;

class ProductlibModel extends Model
{
    protected $DBGroup = 'masterfile';
    protected $table            = 'v_product_lib';
    // protected $primaryKey       = 'id';
    // protected $useAutoIncrement = true;
    protected $returnType       = \App\Entities\Productlib::class;
    protected $useSoftDeletes   = false;
    // protected $protectFields    = true;
    protected $allowedFields    = ['prod_code','prod_desc','prod_cat','p_switch','sector','classification','exclude','sortfield','fair_code'];

    protected $minorMajorFields = [
        'a.prod_code','a.prod_desc','a.prod_cat','a.sector','a.fair_code','b.c_profile','b.switch'
    ];


    protected $logger;

    public function __construct(){
        parent::__construct();
        $this->logger = service('logger');
    }
    //BY PRODUCT DESCRIPTION
    public function getProductDetails($value,$fair_code,$sector){
        try{
            $builder = $this->db->table($this->table.' a')
            ->select($this->minorMajorFields)
            ->join('v_reference b','a.prod_cat=b.c_code')
            ->like('a.sector',$sector)
            ->like('a.fair_code', $fair_code)
            ->where('prod_desc',$value);
            $query = $builder->get();
            $results = $query->getRow();
            // $sql = $this->db->getLastQuery()->getQuery();
            // $results['sql'] = str_replace("\n", " ", $sql);
            // echo $results['sql'];exit();
            return $results;
        }
        catch (DatabaseException $e){
            return $this->handleDatabaseException($e);
        }
        catch(\Exception $e){
            $this->logger->error("Unexpected error: ".$e->getMessage() );
            return [
                'status' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage(),
            ];
        }
    }

    //BY PRODUCT CODE
    public function getProductDetailsByCode($value,$fair_code,$sector){
        try{
            $builder = $this->db->table($this->table.' a')
            ->select($this->minorMajorFields)
            ->join('v_reference b','a.prod_cat=b.c_code')
            ->like('a.sector',$sector)
            ->like('a.fair_code', $fair_code)
            ->where('prod_code',$value);
            $query = $builder->get();
            $results = $query->getRow();
            // $sql = $this->db->getLastQuery()->getQuery();
            // $results['sql'] = str_replace("\n", " ", $sql);
            // echo $results['sql'];exit();
            return $results;
        }
        catch (DatabaseException $e){
            return $this->handleDatabaseException($e);
        }
        catch(\Exception $e){
            $this->logger->error("Unexpected error: ".$e->getMessage() );
            return [
                'status' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage(),
            ];
        }
    }

    public function getProductList($fair_code,$sector){
        try{
            $builder = $this->db->table($this->table.' a')
            ->select($this->minorMajorFields)
            ->join('v_reference b','a.prod_cat=b.c_code','left')
            ->orderBy('c_profile asc,prod_desc asc')
            ->like('a.sector',$sector)
            ->like('a.fair_code', $fair_code)
            ->where('b.exclude!=',1)
            ->where('a.exclude!=',1)
            ->where('switch','GP');
            $query = $builder->get();
            $results = $query->getResult();
            // $sql = $this->db->getLastQuery()->getQuery();
            // $results['sql'] = str_replace("\n", " ", $sql);
            // $sql = str_replace("\n", " ", $sql);
            return $results;
        }
        catch (DatabaseException $e){
            return $this->handleDatabaseException($e);
        }
        catch(\Exception $e){
            $this->logger->error("Unexpected error: ".$e->getMessage() );
            return [
                'status' => false,
                'message' => 'An unexpected error occurred: ' . $e->getMessage(),
            ];
        }
    }

    private function handleDatabaseException(DatabaseException $e){
        $message = $e->getMessage();
        $this->logger->error("Database error: $message");
        // Generic error response
        if(ENVIRONMENT === 'development'){
            if (strpos($message, 'Unknown column') !== false) {
                return [
                    'status' => false,
                    'message' => 'Database error: ' . $message,
                ];
            }
            return [
                'status' => false,
                'message' => 'Error retrieving data' . $message,
                // 'data' => [],
            ];
        }else{
            return [
                'status' => false,
                'message' => 'Error retrieving data. ',
                // 'data' => [],
            ];
        }
    }





}
