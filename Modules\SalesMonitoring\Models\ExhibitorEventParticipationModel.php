<?php

namespace Modules\SalesMonitoring\Models;

use CodeIgniter\Model;

class ExhibitorEventParticipationModel extends Model
{
    protected $DBGroup = 'default';
    protected $table            = 'e_sales_fair';
    protected $primaryKey       = 'id';
    protected $useAutoIncrement = true;
    protected $returnType       = \Modules\SalesMonitoring\Entities\ExhParticipation::class;
    // protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = ['ff_code','event_id','fair_code'];

    // protected bool $allowEmptyInserts = false;
    // protected bool $updateOnlyChanged = true;

    // protected array $casts = [];
    // protected array $castHandlers = [];

    // Dates
    // protected $useTimestamps = false;
    // protected $dateFormat    = 'datetime';
    // protected $createdField  = 'created_at';
    // protected $updatedField  = 'updated_at';
    // protected $deletedField  = 'deleted_at';

    // Validation
    // protected $validationRules      = [];
    // protected $validationMessages   = [];
    // protected $skipValidation       = false;
    // protected $cleanValidationRules = true;

    // Callbacks
    // protected $allowCallbacks = true;
    // protected $beforeInsert   = [];
    // protected $afterInsert    = [];
    // protected $beforeUpdate   = [];
    // protected $afterUpdate    = [];
    // protected $beforeFind     = [];
    // protected $afterFind      = [];
    // protected $beforeDelete   = [];
    // protected $afterDelete    = [];


    public function getEventsByExhId($ff_code){
        return $this->db->table($this->table. ' a')
        ->select([
            'a.event_id',
            'a.ff_code',
            'b.description',
            'b.start_date',
            'a.fair_code',
            'b.no_of_days',
            'theme',
            'b.class_name',
            'enable_during_event_sales',
            'enable_after_event_sales'
        ])
        ->join('e_sales_eventsched b','a.event_id = b.eventid')
        ->join('e_sales_settings c','b.eventid = c.event_id')
        ->where(['b.active!='=>0])
        ->where(['ff_code'=>$ff_code])->get()->getResult();
    }
    
}
